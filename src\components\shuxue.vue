<script setup>
  import mathJaxLoader from '@/utils/mathJaxLoader';
  import MarkdownIt from 'markdown-it';
  import { nextTick, onErrorCaptured, onMounted, onUnmounted, ref, watch } from 'vue';

  // 防抖函数
  const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  const props = defineProps({
    content: {
      type: String,
      default: '',
    },
    isPreformatted: {
      type: Boolean,
      default: false,
    },
    // MathJax 配置（与全局配置呼应，可局部覆盖）
    mathJaxConfig: {
      type: Object,
      default: () => ({
        tex: {
          inlineMath: [
            ['$', '$'],
            ['\\(', '\\)'],
          ],
          displayMath: [
            ['$$', '$$'],
            ['\\[', '\\]'],
          ],
          processEscapes: true,
          packages: { '[+]': ['ams'] }, // 关键：加载AMSmath扩展
        },
      }),
    },
    renderOnMount: {
      type: Boolean,
      default: true,
    },
    renderOnUpdate: {
      type: Boolean,
      default: true,
    },
    // 性能优化配置
    enableCache: {
      type: Boolean,
      default: true,
    },
    debounceDelay: {
      type: Number,
      default: 300,
    },
    // 智能处理开关
    enableSmartMathBlocks: {
      type: Boolean,
      default: false, // 默认禁用，避免破坏表格
    },
  });

  const emits = defineEmits(['render-start', 'render-complete', 'render-error']);

  const rawContent = ref('');
  const mathContainer = ref(null);
  let resizeObserver = null;

  // 表格滚动模式状态管理
  // Map结构: tableIndex -> boolean
  // true: 滚动模式 - 表格可水平滚动，适合包含数学公式的宽表格
  // false: 自适应模式 - 表格自适应容器宽度，内容自动换行
  // 默认模式: 滚动模式(true) - 确保表格内容完全可见，左侧不被隐藏
  const tableScrollModes = ref(new Map()); // 存储每个表格的滚动模式状态

  // 缓存优化
  const renderCache = new Map();

  // 初始化 Markdown 解析器
  const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
    breaks: true,
    escape: false, // 禁用HTML转义，避免$被转义
  });
  // 自动修正 AI 生成的 LaTeX 公式中的 \text{...} 和 \textbf{...} 里的箭头等符号
  // 在文本模式中，应该保持 Unicode 符号或使用文本模式兼容的替代方案
  function fixTextArrowsInLatex(latex) {
    // 🔧 保护扩展箭头命令（如 \xrightarrow, \xleftarrow 等）
    const protectedCommands = [];
    const protectExtendedArrows = (content) => {
      return content.replace(
        /\\x(left|right|Left|Right|leftrightarrow|rightleftarrow)arrow\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g,
        (match) => {
          const placeholder = `__PROTECTED_ARROW_${protectedCommands.length}__`;
          protectedCommands.push(match);
          console.log('🔧 保护扩展箭头命令:', match, '→', placeholder);
          return placeholder;
        },
      );
    };

    // 恢复保护的命令
    const restoreProtectedCommands = (content) => {
      protectedCommands.forEach((cmd, index) => {
        const placeholder = `__PROTECTED_ARROW_${index}__`;

        // 🔧 新增：将扩展箭头命令替换为MathJax原生支持的命令
        let processedCmd = cmd;

        // 处理 \xrightarrow{text} → \overset{text}{\rightarrow}
        if (cmd.includes('\\xrightarrow')) {
          processedCmd = cmd.replace(/\\xrightarrow\{([^}]*)\}/g, (match, text) => {
            if (text.trim() === '') {
              // 空文本情况：\xrightarrow{} → \rightarrow
              console.log('🔧 替换空扩展箭头:', match, '→', '\\rightarrow');
              return '\\rightarrow';
            } else {
              // 有文本情况：\xrightarrow{text} → \overset{text}{\rightarrow}
              const replacement = `\\overset{${text}}{\\rightarrow}`;
              console.log('🔧 替换扩展右箭头:', match, '→', replacement);
              return replacement;
            }
          });
        }

        // 处理 \xleftarrow{text} → \overset{text}{\leftarrow}
        if (cmd.includes('\\xleftarrow')) {
          processedCmd = processedCmd.replace(/\\xleftarrow\{([^}]*)\}/g, (match, text) => {
            if (text.trim() === '') {
              console.log('🔧 替换空扩展箭头:', match, '→', '\\leftarrow');
              return '\\leftarrow';
            } else {
              const replacement = `\\overset{${text}}{\\leftarrow}`;
              console.log('🔧 替换扩展左箭头:', match, '→', replacement);
              return replacement;
            }
          });
        }

        // 处理 \xLeftarrow{text} → \overset{text}{\Leftarrow}
        if (cmd.includes('\\xLeftarrow')) {
          processedCmd = processedCmd.replace(/\\xLeftarrow\{([^}]*)\}/g, (match, text) => {
            if (text.trim() === '') {
              console.log('🔧 替换空扩展箭头:', match, '→', '\\Leftarrow');
              return '\\Leftarrow';
            } else {
              const replacement = `\\overset{${text}}{\\Leftarrow}`;
              console.log('🔧 替换扩展双左箭头:', match, '→', replacement);
              return replacement;
            }
          });
        }

        // 处理 \xRightarrow{text} → \overset{text}{\Rightarrow}
        if (cmd.includes('\\xRightarrow')) {
          processedCmd = processedCmd.replace(/\\xRightarrow\{([^}]*)\}/g, (match, text) => {
            if (text.trim() === '') {
              console.log('🔧 替换空扩展箭头:', match, '→', '\\Rightarrow');
              return '\\Rightarrow';
            } else {
              const replacement = `\\overset{${text}}{\\Rightarrow}`;
              console.log('🔧 替换扩展双右箭头:', match, '→', replacement);
              return replacement;
            }
          });
        }

        content = content.replace(placeholder, processedCmd);

        // 只有当命令被替换时才显示替换日志，否则显示原有的恢复日志
        if (processedCmd !== cmd) {
          console.log('🔧 恢复并替换扩展箭头命令:', placeholder, '→', processedCmd);
        } else {
          console.log('🔧 恢复扩展箭头命令:', placeholder, '→', cmd);
        }
      });
      return content;
    };

    // 先保护扩展箭头命令
    latex = protectExtendedArrows(latex);
    // 处理 \text{} 内容
    latex = latex.replace(/\\text\{([^}]*)\}/g, (_, inner) => {
      console.log('🔍 fixTextArrowsInLatex - 原始 \\text inner:', inner);

      // 在 \text{} 内部，保持 Unicode 箭头符号，因为 \rightarrow 等只能在数学模式中使用
      const replaced = inner
        .replace(/\\rightarrow/g, '→') // 将错误的 \rightarrow 转回 Unicode
        .replace(/\\leftarrow/g, '←')
        .replace(/\\uparrow/g, '↑')
        .replace(/\\downarrow/g, '↓')
        .replace(/\\leq/g, '≤')
        .replace(/\\geq/g, '≥')
        // 修复可能的拼写错误
        .replace(/\\riahtarrow/g, '→') // 修复拼写错误
        .replace(/\\rihtarrow/g, '→') // 修复其他可能的拼写错误
        .replace(/\\righarrow/g, '→'); // 修复其他可能的拼写错误

      console.log('🔍 fixTextArrowsInLatex - 处理后 \\text replaced:', replaced);
      return `\\text{${replaced}}`;
    });

    // 处理 \textbf{} 内容
    latex = latex.replace(/\\textbf\{([^}]*)\}/g, (_, inner) => {
      console.log('🔍 fixTextArrowsInLatex - 原始 \\textbf inner:', inner);

      // 在 \textbf{} 内部，保持 Unicode 箭头符号
      const replaced = inner
        .replace(/\\rightarrow/g, '→') // 将错误的 \rightarrow 转回 Unicode
        .replace(/\\leftarrow/g, '←')
        .replace(/\\uparrow/g, '↑')
        .replace(/\\downarrow/g, '↓')
        .replace(/\\leq/g, '≤')
        .replace(/\\geq/g, '≥')
        // 修复可能的拼写错误
        .replace(/\\riahtarrow/g, '→') // 修复拼写错误
        .replace(/\\rihtarrow/g, '→') // 修复其他可能的拼写错误
        .replace(/\\righarrow/g, '→'); // 修复其他可能的拼写错误

      console.log('🔍 fixTextArrowsInLatex - 处理后 \\textbf replaced:', replaced);
      return `\\textbf{${replaced}}`;
    });

    // 🔧 恢复保护的扩展箭头命令
    latex = restoreProtectedCommands(latex);

    return latex;
  }

  // 🚀 智能数学公式处理器 - 保证 $$...$$ 包裹，但不破坏表格结构
  const processSmartMathBlocks = (content) => {
    // 先修正 AI 生成的 LaTeX 问题
    content = fixTextArrowsInLatex(content);

    // 检测是否在表格或引用块中 - 更精确的检测
    const isInTableOrBlockquote = (text, index) => {
      const beforeText = text.substring(0, index);
      const afterText = text.substring(index);

      // 检查是否在表格中
      const lastTableRow = beforeText.lastIndexOf('|');
      const nextTableRow = afterText.indexOf('|');

      if (lastTableRow !== -1 && nextTableRow !== -1) {
        const beforeDistance = index - lastTableRow;
        const afterDistance = nextTableRow;

        if (beforeDistance < 100 && afterDistance < 100) {
          const contextText =
            beforeText.substring(lastTableRow) + afterText.substring(0, nextTableRow);
          if (/\|[\s\-:]+\|/.test(contextText) || contextText.split('|').length > 2) {
            return true;
          }
        }
      }

      // 检查是否在引用块中
      // 查找最近的引用块标记
      const lines = beforeText.split('\n');
      for (let i = lines.length - 1; i >= 0; i--) {
        const line = lines[i].trim();
        if (line.startsWith('>')) {
          // 在引用块中
          return true;
        }
        if (line !== '' && !line.startsWith('>')) {
          // 遇到非空非引用行，说明不在引用块中
          break;
        }
      }

      return false;
    };

    // 处理数学公式块的通用函数
    const processMathBlock = (content, regex, wrapper) => {
      return content.replace(regex, (match, mathContent, offset) => {
        // 检查是否在表格或引用块中
        if (isInTableOrBlockquote(content, offset)) {
          // 在表格或引用块中，保持原样，不转换为独立块
          return match;
        }

        // 不在表格或引用块中，转换为独立的数学块
        const cleanedMath = mathContent.trim().replace(/^\\\\/, ''); // 删掉开头的\\
        return `\n\n<div class="smart-math-block-silent tex2jax_process">\n${wrapper[0]}\n${cleanedMath}\n${wrapper[1]}\n</div>\n\n`;
      });
    };

    // 匹配数学公式块 $$ ... $$
    const mathBlockRegex = /\$\$([\s\S]*?)\$\$/g;
    content = processMathBlock(content, mathBlockRegex, ['$$', '$$']);

    // 匹配数学公式块 \[ ... \]
    const mathBracketRegex = /\\\[([\s\S]*?)\\\]/g;
    content = processMathBlock(content, mathBracketRegex, ['\\[', '\\]']);

    return content;
  };

  // 🚀 智能代码块处理器
  const processSmartCodeBlocks = (content) => {
    console.log('🔧 开始处理智能代码块...');

    // 匹配代码块 ```...```
    const codeBlockRegex = /```[\s\S]*?```/g;

    return content.replace(codeBlockRegex, (match) => {
      // 提取代码块内容（去掉```标记）
      const codeContent = match.replace(/^```[\w]*\n?/, '').replace(/\n?```$/, '');

      // 检查是否包含Markdown格式或数学公式
      const hasMarkdown = /\*\*[^*]+\*\*|__[^_]+__|`[^`]+`|^\s*[►▶•\-\*]\s/m.test(codeContent);
      const hasMath = /\$\$[\s\S]*?\$\$|\$[^$]+\$|\\[a-zA-Z]+\{/.test(codeContent);

      console.log('📝 代码块分析:', {
        hasMarkdown,
        hasMath,
        preview: codeContent.substring(0, 100) + '...',
      });

      if (hasMarkdown || hasMath) {
        console.log('✨ 发现需要渲染的代码块，转换为特殊格式');

        // 🎯 关键策略：将智能代码块转换为特殊的div，而不是pre/code
        // 这样可以让Markdown和MathJax正常处理其中的内容
        return `\n\n<div class="smart-code-block">\n\n${codeContent}\n\n</div>\n\n`;
      }

      // 普通代码块保持不变
      return match;
    });
  };

  // 渲染 Markdown 内容（重构为更稳健的“隔离-修复-还原”逻辑）
  const renderMarkdown = (content) => {
    // 1. 打印传入的、未经处理的原始数据
    console.log('--- 渲染前: 传入的原始数据 ---', content);
    if (!content) return '';

    // 🔧 全局扩展箭头命令替换（在所有处理之前进行）
    content = content.replace(/\\xrightarrow\{([^}]*)\}/g, (match, text) => {
      if (text.trim() === '') {
        console.log('🔧 全局替换空扩展右箭头:', match, '→', '\\rightarrow');
        return '\\rightarrow';
      } else {
        const replacement = `\\overset{${text}}{\\rightarrow}`;
        console.log('🔧 全局替换扩展右箭头:', match, '→', replacement);
        return replacement;
      }
    });

    content = content.replace(/\\xleftarrow\{([^}]*)\}/g, (match, text) => {
      if (text.trim() === '') {
        console.log('🔧 全局替换空扩展左箭头:', match, '→', '\\leftarrow');
        return '\\leftarrow';
      } else {
        const replacement = `\\overset{${text}}{\\leftarrow}`;
        console.log('🔧 全局替换扩展左箭头:', match, '→', replacement);
        return replacement;
      }
    });

    content = content.replace(/\\xLeftarrow\{([^}]*)\}/g, (match, text) => {
      if (text.trim() === '') {
        console.log('🔧 全局替换空扩展双左箭头:', match, '→', '\\Leftarrow');
        return '\\Leftarrow';
      } else {
        const replacement = `\\overset{${text}}{\\Leftarrow}`;
        console.log('🔧 全局替换扩展双左箭头:', match, '→', replacement);
        return replacement;
      }
    });

    content = content.replace(/\\xRightarrow\{([^}]*)\}/g, (match, text) => {
      if (text.trim() === '') {
        console.log('🔧 全局替换空扩展双右箭头:', match, '→', '\\Rightarrow');
        return '\\Rightarrow';
      } else {
        const replacement = `\\overset{${text}}{\\Rightarrow}`;
        console.log('🔧 全局替换扩展双右箭头:', match, '→', replacement);
        return replacement;
      }
    });

    // 缓存检查 - 添加版本控制和内容特征
    const contentHash = content.length + '_' + (content.match(/\*\*/g) || []).length; // 包含加粗标记数量
    const cacheKey = `v12_${contentHash}_${content.substring(0, 50)}`; // 更新版本，包含内容特征
    if (props.enableCache && renderCache.has(cacheKey)) {
      console.log('--- 使用缓存渲染 ---', cacheKey.substring(0, 30) + '...');
      return renderCache.get(cacheKey);
    }

    emits('render-start');

    // 🚀 第0步：智能预处理（在所有其他处理之前）
    if (props.enableSmartMathBlocks) {
      content = processSmartMathBlocks(content); // 先处理数学公式
    }
    content = processSmartCodeBlocks(content); // 再处理代码块

    // 语法净化：在解析前，强制为所有“紧贴”着内容的表格前添加一个换行符。
    // 这可以解决因缺少空行而导致表格被错误地并入前方列表项或段落的问题。
    const tableFixRegex = /([^\n])\n(\s*\|[^\n]+\|\s*\n\s*\|[-:|\s]+\|)/g;
    content = content.replace(tableFixRegex, '$1\n\n$2');

    // --- 第 1 步：完全隔离 ---
    // 将所有数学公式（以及需要被当作公式处理的命令）提取到数组中，用占位符替换
    const mathExpressions = [];
    const replaceWithPlaceholder = (match) => {
      mathExpressions.push(match);
      return `@@MATH_PLACEHOLDER_${mathExpressions.length - 1}@@`;
    };

    let processedContent = content.replace(/\\\\/g, '\\'); // 规范化反斜杠

    // 修复单独的 "math" 关键词，将其替换为完整的 $$ 数学公式块
    if (processedContent.includes('math')) {
      console.log('🔍 发现 math 关键词，开始修复...');
      const mathIndex = processedContent.indexOf('math');
      console.log('修复前片段:', processedContent.substring(mathIndex - 30, mathIndex + 80));

      // 基于真实数据的精确模式：math 后面的内容直到 "  \n\n---" 结束
      const mathPattern = /math\s+\n([\s\S]*?)(?=\s*\n\n---|\s*\n\n###|\s*\n\n\*\*|$)/;
      const mathMatch = processedContent.match(mathPattern);

      if (mathMatch) {
        console.log('找到 math 块，长度:', mathMatch[0].length);
        console.log('math 内容预览:', mathMatch[1].substring(0, 100) + '...');
        // 将整个 math 块替换为 $$ 包围的数学公式
        const mathContent = mathMatch[1].trim();
        const replacement = `$$\n${mathContent}\n$$`;
        processedContent = processedContent.replace(mathPattern, replacement);
        console.log('✅ math 关键词修复完成');
        console.log('替换内容长度:', replacement.length);
        console.log('替换内容预览:', replacement.substring(0, 100) + '...');
      } else {
        console.log('⚠️ 未找到 math 块模式，尝试简单替换...');
        processedContent = processedContent.replace(/math\s+\n/g, '$$\n');
      }
    }

    // 依次提取标准定界符公式 - 优化版本
    // 1. 先处理块级公式（$$...$$）
    processedContent = processedContent.replace(/\$\$([\s\S]+?)\$\$/g, replaceWithPlaceholder);

    // 2. 处理LaTeX块级公式
    processedContent = processedContent.replace(/\\\[([\s\S]+?)\\\]/g, replaceWithPlaceholder);

    // 3. 处理LaTeX行内公式
    processedContent = processedContent.replace(/\\\(([\s\S]+?)\\\)/g, replaceWithPlaceholder);

    // 4. 处理行内公式（$...$）- 改进的正则表达式
    processedContent = processedContent.replace(
      /\$([^$\n]+(?:\n[^$\n]*)*[^$\n]*)\$/g,
      replaceWithPlaceholder,
    );

    // 提取 "裸奔" 的 \boxed 命令，并直接为其包裹$$后存入数组
    processedContent = processedContent.replace(/(\\boxed\{(?:[^{}]|\{[^{}]*\})+\})/g, (match) => {
      return replaceWithPlaceholder(`$$${match}$$`);
    });

    // 核心修复：新增对“裸奔”的 \textbf 命令的识别和包裹
    processedContent = processedContent.replace(/(\\textbf\{(?:[^{}]|\{[^{}]*\})+\})/g, (match) => {
      return replaceWithPlaceholder(`$${match}$`);
    });

    // 疫苗加强：新增对“裸奔”的 \bf (旧版) 命令的识别和包裹
    processedContent = processedContent.replace(/(\\bf\{(?:[^{}]|\{[^{}]*\})+\})/g, (match) => {
      return replaceWithPlaceholder(`$${match}$`);
    });

    // --- 第 2 步：安全修复 ---
    // 只在隔离出的 mathExpressions 数组内部，安全地修复公式
    for (let i = 0; i < mathExpressions.length; i++) {
      // 最终修正：使用标准的 \textcolor 命令，并保持最稳健的“隔离-修复-还原”逻辑
      // 保持原有的 \color 语法，不进行转换

      // 修复TikZ绘图语法 - 将复杂的TikZ交叉符号替换为简单的数学符号
      mathExpressions[i] = mathExpressions[i].replace(
        /\\mathbin\{\\tikz\[.*?\]\\draw.*?\}/g,
        '\\textcolor{red}{\\times}', // 使用红色乘号表示交叉关系
      );

      // 更精确的TikZ交叉符号替换（针对具体的交叉线绘制）
      mathExpressions[i] = mathExpressions[i].replace(
        /\\tikz\[x=1\.4ex,y=1\.4ex,line width=\.2ex, red\] \\draw \(0,0\) -- \(1,1\) \(0,1\) -- \(1,0\);/g,
        '\\textcolor{red}{\\times}', // 红色乘号表示交叉
      );

      // 通用TikZ清理 - 移除所有TikZ相关语法
      mathExpressions[i] = mathExpressions[i].replace(
        /\\tikz\[.*?\].*?;/g,
        '\\textcolor{red}{\\times}',
      );

      // 额外的TikZ语法清理
      mathExpressions[i] = mathExpressions[i].replace(
        /\\mathbin\{[^}]*tikz[^}]*\}/g,
        '\\textcolor{red}{\\times}',
      );

      // 清理可能残留的TikZ命令
      mathExpressions[i] = mathExpressions[i].replace(/\\draw\s*\([^)]*\)\s*--\s*\([^)]*\)/g, '');

      // 修复常见的LaTeX语法问题 - 只修复明确的错误

      // 1. 修复 \[数字pt] 换行语法错误
      mathExpressions[i] = mathExpressions[i].replace(/\\(\[\d+pt\])/g, '\\\\$1');

      // 2. 修复cases环境中缺少的反斜杠
      mathExpressions[i] = mathExpressions[i].replace(
        /(\\begin\{cases\}[\s\S]*?)\\(\s+[A-Za-z])/g,
        '$1\\\\$2',
      );

      // 3. 🔧 新增：修复换行符导致的公式断裂问题
      // 确保数学公式中的换行符被正确处理
      mathExpressions[i] = mathExpressions[i].replace(/\n\s*\n/g, ' ');

      // 修复可能的HTML实体编码问题
      mathExpressions[i] = mathExpressions[i].replace(/&amp;/g, '&');
      mathExpressions[i] = mathExpressions[i].replace(/&lt;/g, '<');
      mathExpressions[i] = mathExpressions[i].replace(/&gt;/g, '>');

      // 🔧 新增：修复 LaTeX 命令兼容性问题
      // 先处理 \text{} 内部的内容，确保箭头符号在文本模式中保持为 Unicode
      mathExpressions[i] = fixTextArrowsInLatex(mathExpressions[i]);

      // 添加调试日志
      console.log('🔍 处理前的数学表达式:', mathExpressions[i]);

      mathExpressions[i] = mathExpressions[i]
        // 保持 textcolor 和 color 命令不变，让 MathJax 自行处理
        // 修复 crossout 命令（使用 cancel）
        .replace(/\\crossout\{([^}]+)\}/g, '\\cancel{$1}')
        // 修复特殊字符
        .replace(/❗/g, '!')
        // 智能处理箭头：使用保护-替换-恢复的策略
        // 1. 先保护 \text{} 和 \textbf{} 内容
        .replace(/\\text\{([^}]*)\}/g, (_, inner) => {
          // 使用特殊标记保护 \text{} 内容，使用浏览器兼容的 btoa
          return `__TEXT_PROTECTED_${btoa(encodeURIComponent(inner))}_END__`;
        })
        .replace(/\\textbf\{([^}]*)\}/g, (_, inner) => {
          // 使用特殊标记保护 \textbf{} 内容，使用浏览器兼容的 btoa
          return `__TEXTBF_PROTECTED_${btoa(encodeURIComponent(inner))}_END__`;
        })
        // 2. 在数学模式中转换箭头
        .replace(/→/g, '\\rightarrow')
        .replace(/←/g, '\\leftarrow')
        .replace(/↑/g, '\\uparrow')
        .replace(/↓/g, '\\downarrow')
        // 3. 恢复 \text{} 和 \textbf{} 内容
        .replace(/__TEXT_PROTECTED_([A-Za-z0-9+/=]+)_END__/g, (_, encoded) => {
          const inner = decodeURIComponent(atob(encoded));
          return `\\text{${inner}}`;
        })
        .replace(/__TEXTBF_PROTECTED_([A-Za-z0-9+/=]+)_END__/g, (_, encoded) => {
          const inner = decodeURIComponent(atob(encoded));
          return `\\textbf{${inner}}`;
        })
        // 修复可能的拼写错误
        .replace(/\\riahtarrow/g, '\\rightarrow')
        .replace(/\\rihtarrow/g, '\\rightarrow')
        .replace(/\\righarrow/g, '\\rightarrow')
        // 确保中文在 text 命令中正确处理
        .replace(/\\text\{([^}]*[一-龟][^}]*)\}/g, '\\text{$1}');

      // 添加调试日志
      console.log('🔍 处理后的数学表达式:', mathExpressions[i]);

      // 🔧 新增：修复 \hline 位置错误问题
      // \hline 命令只能在 LaTeX 表格环境中的正确位置使用
      // 正确位置：在 \\ 换行符之后，或在表格行的开始/结束位置
      if (mathExpressions[i].includes('\\hline')) {
        console.log('🔍 检测到 \\hline 命令，开始修复...');

        // 检测是否在表格环境中（array, tabular, matrix 等）
        const hasTableEnvironment =
          /\\begin\{(array|tabular|matrix|pmatrix|bmatrix|vmatrix|Vmatrix)\}/.test(
            mathExpressions[i],
          );

        if (hasTableEnvironment) {
          // 在表格环境中，修复 \hline 位置错误
          // 1. 修复缺少换行符的情况：& \ \hline -> & \\ \hline
          mathExpressions[i] = mathExpressions[i].replace(/(&\s*)\\(\s*\\hline)/g, '$1\\\\$2');

          // 2. 修复单个反斜杠的情况：& \ \hline -> & \\ \hline
          mathExpressions[i] = mathExpressions[i].replace(/(&\s*)\\(\s+\\hline)/g, '$1\\\\$2');

          // 3. 修复行首的 \hline 前缺少换行符的情况
          mathExpressions[i] = mathExpressions[i].replace(
            /(\\begin\{[^}]+\}[^\\]*?)\\hline/g,
            '$1\\\\ \\hline',
          );

          // 4. 修复连续的反斜杠问题：\\\ \hline -> \\ \hline
          mathExpressions[i] = mathExpressions[i].replace(/\\{3,}\s*\\hline/g, '\\\\ \\hline');

          // 5. 确保 \hline 前后有适当的空格
          mathExpressions[i] = mathExpressions[i].replace(/\\hline(?!\s)/g, '\\hline ');
          mathExpressions[i] = mathExpressions[i].replace(/(?<!\\\\)\s*\\hline/g, ' \\hline');

          console.log('✅ \\hline 位置已修复');
        } else {
          // 不在表格环境中，移除 \hline 命令以避免错误
          console.log('⚠️ \\hline 不在表格环境中，将被移除');
          mathExpressions[i] = mathExpressions[i].replace(/\\hline\s*/g, '');
        }

        console.log('🔍 \\hline 修复后的表达式:', mathExpressions[i]);
      }

      // 3. 修复cases环境中的换行问题 - 更精确的匹配
      mathExpressions[i] = mathExpressions[i].replace(
        /(\\begin\{cases\}[\s\S]*?)([^\\])(\s*\n\s*)([A-Za-z\\])/g,
        '$1$2 \\\\$3$4',
      );

      // 4. 修复单独的换行符问题（不在cases环境中）
      mathExpressions[i] = mathExpressions[i].replace(/([^\\])\s*\n\s*([A-Za-z\\])/g, '$1 \\\\ $2');

      // 5. 修复多行公式中的换行问题
      mathExpressions[i] = mathExpressions[i].replace(
        /([^\\])\s*\n\s*(\\text\{[^}]*\})/g,
        '$1 \\\\ $2',
      );
    }

    // --- 第 3 步：渲染与还原 ---
    // 渲染不含任何公式的纯 Markdown 文本
    let rendered = md.render(processedContent);

    // 将修复好的公式精确地还原到它们原来的位置 - 优化版本
    rendered = rendered.replace(/@@MATH_PLACEHOLDER_(\d+)@@/g, (match, index) => {
      const formula = mathExpressions[parseInt(index, 10)];

      // 检查是否是块级公式
      if (formula.startsWith('$$') && formula.endsWith('$$')) {
        // 🔧 新增：检查是否应该转换为内联模式
        const inner = formula.slice(2, -2).trim();

        // 判断是否为简单表达式（单个数字、简单比例、短表达式）
        const isSimpleExpression =
          /^[0-9]+$/.test(inner) ||
          /^[0-9]+:[0-9]+$/.test(inner) ||
          /^[0-9]+\s*\\\\\s*[0-9]+$/.test(inner) ||
          (inner.length < 20 && !inner.includes('\\\\') && !inner.includes('\\begin'));

        // 🔧 新增：检查是否在列表项、引用块或表格中，如果是则强制转换为内联模式
        const beforeMatch = rendered.substring(0, rendered.indexOf(match));
        const isInListItem =
          /<li[^>]*>[^<]*$/.test(beforeMatch) ||
          /^\s*-\s+[^-]*$/.test(beforeMatch.split('\n').pop() || '');

        // 检测是否在引用块中：计算未闭合的 blockquote 标签数量
        const blockquoteOpenCount = (beforeMatch.match(/<blockquote[^>]*>/g) || []).length;
        const blockquoteCloseCount = (beforeMatch.match(/<\/blockquote>/g) || []).length;
        const isInBlockquote = blockquoteOpenCount > blockquoteCloseCount;

        // 🔧 新增：检测是否在表格单元格中
        const tableOpenCount = (beforeMatch.match(/<table[^>]*>/g) || []).length;
        const tableCloseCount = (beforeMatch.match(/<\/table>/g) || []).length;
        const tdOpenCount = (beforeMatch.match(/<t[dh][^>]*>/g) || []).length;
        const tdCloseCount = (beforeMatch.match(/<\/t[dh]>/g) || []).length;
        const isInTable = tableOpenCount > tableCloseCount;
        const isInTableCell = tdOpenCount > tdCloseCount;

        // 🔧 扩展简单表达式的定义，包含常见的分数表达式
        const isSimpleFraction =
          /^\\frac\{[^}]{1,10}\}\{[^}]{1,10}\}(\s*[<>=]\s*\\frac\{[^}]{1,10}\}\{[^}]{1,10}\})*$/.test(
            inner,
          );
        const isExtendedSimpleExpression = isSimpleExpression || isSimpleFraction;

        if (
          isExtendedSimpleExpression ||
          isInListItem ||
          isInBlockquote ||
          isInTable ||
          isInTableCell
        ) {
          // 转换为内联模式
          const reason = isInListItem
            ? '(在列表项中)'
            : isInBlockquote
              ? '(在引用块中)'
              : isInTable
                ? '(在表格中)'
                : isInTableCell
                  ? '(在表格单元格中)'
                  : isSimpleFraction
                    ? '(简单分数表达式)'
                    : '';
          console.log('🔧 转换为内联模式:', inner, reason);
          return `$${inner}$`;
        } else {
          // 保持块级模式，确保前后有换行符
          return `\n${formula}\n`;
        }
      }

      return formula;
    });

    // 清理 Markdown-it 可能在块级公式外添加的多余 <p> 标签
    rendered = rendered.replace(/<p>\s*(\$\$[\s\S]+?\$\$|\\\[[\s\S]+?\\\])\s*<\/p>/g, '$1');

    // 修复引用块中被转义的数学公式
    rendered = rendered.replace(
      /<blockquote>\s*<p>([\s\S]*?)<\/p>\s*<\/blockquote>/g,
      (_, content) => {
        console.log('🔍 修复前的引用块内容:', content);

        // 在引用块中恢复被转义的数学公式
        const fixedContent = content
          .replace(/\\\\\s*\\\[/g, '\\[') // 修复 \\ \[ 被转义
          .replace(/\\\\\s*\\\]/g, '\\]') // 修复 \\ \] 被转义
          .replace(/\\\\\s*\[/g, '\\[') // 修复 \\ [ 被转义
          .replace(/\\\\\s*\]/g, '\\]') // 修复 \\ ] 被转义
          .replace(/\\\\?\[/g, '\\[') // 修复 \[ 被转义
          .replace(/\\\\?\]/g, '\\]') // 修复 \] 被转义
          .replace(/\\\[(\s*\\\\)/g, '\\[$1') // 修复 \[ \\ 的情况
          .replace(/(\\\\)\s*\\\]/g, '$1\\]') // 修复 \\ \] 的情况
          .replace(/\\\[\s*\\\\/g, '\\[') // 修复 \[ \\ 开头
          .replace(/\\\\\s*\\\]/g, '\\]') // 修复 \\ \] 结尾
          .replace(/&lt;/g, '<') // 修复 < 被转义
          .replace(/&gt;/g, '>'); // 修复 > 被转义

        console.log('🔍 修复后的引用块内容:', fixedContent);

        // 为引用块中的数学公式添加 MathJax 处理类
        const finalContent = fixedContent.replace(
          /(\\\[[\s\S]*?\\\])/g,
          '<span class="tex2jax_process">$1</span>',
        );

        return `<blockquote><p>${finalContent}</p></blockquote>`;
      },
    );

    // 修复可能被错误分割的数学公式
    rendered = rendered.replace(/\$\$\s*<\/p>\s*<p>\s*([^$]+?)\$\$/g, '$$\n$1\n$$');

    // 🔧 新增：清理干扰MathJax渲染的cite元素
    rendered = rendered.replace(/<cite[^>]*>.*?<\/cite>/g, '');
    console.log('🧹 清理cite元素完成');

    // 样式增强（保留）- 为表格添加切换功能
    let tableIndex = 0;
    rendered = rendered.replace(/<table>/g, () => {
      const currentIndex = tableIndex++;
      console.log('🏗️ 生成表格HTML，索引:', currentIndex);
      return `<div class="table-wrapper my-4" data-table-wrapper="${currentIndex}">
        <div class="table-controls mb-2 flex justify-end items-center">
          <button
            class="table-toggle-btn px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-md flex items-center gap-1"
            data-table-index="${currentIndex}"
          >
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
            </svg>
            <span class="toggle-text">切换到滚动</span>
          </button>
        </div>
        <div class="table-container wrap-mode" data-table-index="${currentIndex}">
          <table class="w-full">`;
    });
    rendered = rendered.replace(/<\/table>/g, '</table></div></div>');

    // 🔧 关键修复：为包含数学公式的代码块添加特殊类名
    rendered = rendered.replace(
      /<pre><code/g,
      '<pre class="bg-gray-800 text-gray-100 p-4 rounded my-4 overflow-x-auto tex2jax_process"><code class="tex2jax_process"',
    );

    emits('render-complete');

    // 2. 打印即将插入页面的、渲染后的 HTML 数据
    console.log('--- 渲染后: 生成的 HTML 数据 ---', rendered);

    // 调试：检查表格HTML结构
    if (rendered.includes('table-container')) {
      console.log('✅ 检测到表格容器HTML');
      const tableMatches = rendered.match(/data-table-index="\d+"/g);
      console.log('🔍 找到的表格索引:', tableMatches);
    }

    // 存储到缓存（限制缓存大小）
    if (props.enableCache) {
      if (renderCache.size > 50) {
        const firstKey = renderCache.keys().next().value;
        renderCache.delete(firstKey);
      }
      renderCache.set(cacheKey, rendered);
    }

    return rendered;
  };

  // 渲染数学公式（MathJax 适配）- 优化版本
  let mathRenderTimeout = null;
  const renderMath = async () => {
    if (!mathContainer.value) return;
    await nextTick();
    // 🔧 防抖MathJax渲染，避免频繁调用
    clearTimeout(mathRenderTimeout);
    mathRenderTimeout = setTimeout(async () => {
      try {
        // 🚀 首先确保MathJax已动态加载
        await mathJaxLoader.loadMathJax();

        // 健壮地等待 MathJax 加载和初始化完成
        if (window.MathJax?.startup?.promise) {
          await window.MathJax.startup.promise;
        } else {
          // 如果 promise 不可用，提供一个备用等待机制
          await new Promise((resolve, reject) => {
            const timeout = 5000; // 增加超时时间
            let waited = 0;
            const interval = 100;
            const check = setInterval(() => {
              if (window.MathJax?.typesetPromise) {
                clearInterval(check);
                return resolve();
              }
              waited += interval;
              if (waited >= timeout) {
                clearInterval(check);
                return reject(new Error('MathJax 初始化超时'));
              }
            }, interval);
          });
        }

        // 等待 Vue DOM 更新
        await nextTick();

        // 🔧 检查是否真的有数学公式需要渲染（包括代码块中的）
        const mathElements = mathContainer.value.querySelectorAll(
          'script[type*="math"], .math, [class*="math"], mjx-container',
        );
        const containerHTML = mathContainer.value.innerHTML;
        const hasUnrenderedMath =
          containerHTML.includes('$') ||
          containerHTML.includes('\\[') ||
          containerHTML.includes('\\(') ||
          containerHTML.includes('\\textcolor') ||
          containerHTML.includes('\\color') ||
          containerHTML.includes('\\Rightarrow') ||
          containerHTML.includes('\\xrightarrow') ||
          containerHTML.includes('\\in') ||
          containerHTML.includes('\\subset');

        console.log('🔍 MathJax检测详情:', {
          hasUnrenderedMath,
          mathElementsCount: mathElements.length,
          containerLength: containerHTML.length,
          hasDollar: containerHTML.includes('$'),
          hasRightarrow: containerHTML.includes('\\Rightarrow'),
          hasXrightarrow: containerHTML.includes('\\xrightarrow'),
          hasIn: containerHTML.includes('\\in'),
          hasSubset: containerHTML.includes('\\subset'),
          sampleMath: containerHTML.match(/\$[^$]+\$/g)?.slice(0, 5) || [],
          sampleHTML: containerHTML.substring(0, 200) + '...',
        });

        // 🔧 强制渲染：如果检测到任何数学符号就渲染
        if (!hasUnrenderedMath && mathElements.length === 0) {
          console.log('🔍 没有发现需要渲染的数学公式，跳过MathJax渲染');
          return;
        }

        console.log('🧮 开始MathJax渲染...');

        // 🔧 强制清理之前的渲染结果
        if (window.MathJax.typesetClear) {
          window.MathJax.typesetClear([mathContainer.value]);
          console.log('🧹 清理之前的MathJax渲染');
        }

        // 🔧 强制重新扫描和渲染
        await window.MathJax.typesetPromise([mathContainer.value]);
        mathContainer.value.classList.add('math-rendered');

        // 🔧 验证渲染结果
        const renderedElements = mathContainer.value.querySelectorAll('mjx-container');
        console.log('✅ MathJax渲染完成，渲染了', renderedElements.length, '个公式');

        // 🔧 初始化所有表格为默认滚动模式
        initializeTableStates();

        // 如果没有渲染任何公式，尝试再次渲染
        if (renderedElements.length === 0 && hasUnrenderedMath) {
          console.log('⚠️ 没有渲染任何公式，尝试再次渲染...');
          setTimeout(async () => {
            try {
              await window.MathJax.typesetPromise([mathContainer.value]);
              const retryElements = mathContainer.value.querySelectorAll('mjx-container');
              console.log('🔄 重试渲染完成，渲染了', retryElements.length, '个公式');

              // 🔧 重试渲染后也需要初始化表格状态
              initializeTableStates();
            } catch (retryError) {
              console.error('🔄 重试渲染失败:', retryError);
            }
          }, 500);
        }
      } catch (error) {
        console.error('❌ MathJax 渲染失败:', error);
        emits('render-error', error);
      }
    }, 200); // 200ms防抖
  };

  // 响应式重新渲染
  const handleResize = () => {
    if (mathContainer.value) {
      renderMath();

      // 🔧 窗口大小改变时，重新检测所有表格的滚动需求
      setTimeout(() => {
        const tableContainers = mathContainer.value?.querySelectorAll(
          '.table-container[data-table-index]',
        );

        if (tableContainers) {
          tableContainers.forEach((container) => {
            const tableIndex = parseInt(container.getAttribute('data-table-index'));
            if (!isNaN(tableIndex)) {
              console.log(`🔄 窗口大小改变，重新检测表格 ${tableIndex}`);
              updateTableDisplay(tableIndex);
            }
          });
        }
      }, 100); // 延迟100ms确保DOM更新完成
    }
  };

  // 初始化 - 优化版本
  const initialize = async () => {
    if (!props.content) return;

    try {
      console.log('🚀 Shuxue组件初始化，内容长度:', props.content.length);

      // 渲染 Markdown 内容
      if (props.isPreformatted) {
        rawContent.value = props.content;
      } else {
        rawContent.value = renderMarkdown(props.content);
      }

      if (props.renderOnMount) {
        // 🔧 检查是否包含数学公式再决定是否渲染MathJax
        const hasMath =
          props.content.includes('$') ||
          props.content.includes('\\[') ||
          props.content.includes('\\(');
        console.log('🧮 内容是否包含数学公式:', hasMath);

        if (hasMath) {
          console.log('📐 开始初始化MathJax渲染...');
          await renderMath();
        } else {
          console.log('⏭️ 跳过MathJax渲染（无数学公式）');
          // 🔧 即使没有数学公式，也需要初始化表格状态
          setTimeout(() => {
            initializeTableStates();
          }, 100);
        }
      }

      // 监听窗口大小变化
      if (window.ResizeObserver) {
        resizeObserver = new ResizeObserver(handleResize);
        if (mathContainer.value) {
          resizeObserver.observe(mathContainer.value);
        }
      }
    } catch (error) {
      console.error('❌ 初始化失败:', error);
      emits('render-error', error);
    }
  };

  onMounted(initialize);

  // 调试函数：输出表格的所有样式信息
  const debugTableStyles = (container, tableIndex) => {
    console.log(`🔍 表格 ${tableIndex} 调试信息:`);

    // 容器信息
    const containerStyles = window.getComputedStyle(container);
    console.log('📦 容器样式:', {
      width: containerStyles.width,
      height: containerStyles.height,
      overflowX: containerStyles.overflowX,
      overflowY: containerStyles.overflowY,
      display: containerStyles.display,
      position: containerStyles.position,
      className: container.className,
      scrollWidth: container.scrollWidth,
      clientWidth: container.clientWidth,
      offsetWidth: container.offsetWidth,
    });

    // 表格信息
    const table = container.querySelector('table');
    if (table) {
      const tableStyles = window.getComputedStyle(table);
      console.log('📋 表格样式:', {
        width: tableStyles.width,
        minWidth: tableStyles.minWidth,
        maxWidth: tableStyles.maxWidth,
        tableLayout: tableStyles.tableLayout,
        whiteSpace: tableStyles.whiteSpace,
        scrollWidth: table.scrollWidth,
        clientWidth: table.clientWidth,
        offsetWidth: table.offsetWidth,
      });

      // 表头信息
      const headers = table.querySelectorAll('th');
      console.log(
        '📑 表头信息:',
        Array.from(headers).map((th, index) => {
          const thStyles = window.getComputedStyle(th);
          return {
            index: index,
            text: th.textContent.trim().substring(0, 20) + '...',
            width: thStyles.width,
            minWidth: thStyles.minWidth,
            maxWidth: thStyles.maxWidth,
            whiteSpace: thStyles.whiteSpace,
            overflow: thStyles.overflow,
            textOverflow: thStyles.textOverflow,
            scrollWidth: th.scrollWidth,
            clientWidth: th.clientWidth,
            offsetWidth: th.offsetWidth,
          };
        }),
      );

      // 第一行数据信息
      const firstRowCells = table.querySelectorAll('tbody tr:first-child td');
      if (firstRowCells.length > 0) {
        console.log(
          '📊 第一行数据:',
          Array.from(firstRowCells).map((td, index) => {
            const tdStyles = window.getComputedStyle(td);
            return {
              index: index,
              text: td.textContent.trim().substring(0, 20) + '...',
              width: tdStyles.width,
              minWidth: tdStyles.minWidth,
              maxWidth: tdStyles.maxWidth,
              whiteSpace: tdStyles.whiteSpace,
              overflow: tdStyles.overflow,
              textOverflow: tdStyles.textOverflow,
              scrollWidth: td.scrollWidth,
              clientWidth: td.clientWidth,
              offsetWidth: td.offsetWidth,
            };
          }),
        );
      }
    }

    // 按钮信息
    const toggleBtn = document.querySelector(
      `button.table-toggle-btn[data-table-index="${tableIndex}"]`,
    );
    if (toggleBtn) {
      console.log('🔘 切换按钮:', {
        text: toggleBtn.textContent.trim(),
        visible: toggleBtn.offsetParent !== null,
        className: toggleBtn.className,
      });
    }

    console.log('---分割线---');
  };

  // 初始化所有表格的默认状态
  const initializeTableStates = () => {
    console.log('🔧 初始化表格默认状态...');

    // 查找所有表格容器
    const tableContainers = mathContainer.value?.querySelectorAll(
      '.table-container[data-table-index]',
    );

    if (tableContainers) {
      tableContainers.forEach((container) => {
        const tableIndex = parseInt(container.getAttribute('data-table-index'));
        if (!isNaN(tableIndex)) {
          console.log(`📊 初始化表格 ${tableIndex} 为滚动模式`);

          // 调试：输出表格的详细信息
          debugTableStyles(container, tableIndex);

          // 不需要设置Map值，因为getTableScrollMode已经默认返回true
          // 直接更新显示状态
          updateTableDisplay(tableIndex);
        }
      });

      console.log(`✅ 已初始化 ${tableContainers.length} 个表格为自适应模式`);
    }
  };

  // 切换表格滚动模式
  const toggleTableScrollMode = (tableIndex) => {
    // 获取当前模式，如果未设置则默认为滚动模式(true)
    // true: 滚动模式 - 表格可水平滚动，内容不换行
    // false: 自适应模式 - 表格自适应容器宽度，内容自动换行
    const currentMode = tableScrollModes.value.get(tableIndex) ?? true; // 默认为滚动模式
    tableScrollModes.value.set(tableIndex, !currentMode);
  };

  // 获取表格滚动模式
  const getTableScrollMode = (tableIndex) => {
    // 返回指定表格的显示模式
    // true: 滚动模式 - 表格固定宽度，可水平滚动，数学公式完整显示
    // false: 自适应模式 - 表格自适应容器宽度，内容换行显示
    // 默认值: true (滚动模式) - 修改为滚动模式，确保表格内容完全可见
    return tableScrollModes.value.get(tableIndex) ?? true; // 默认为滚动模式
  };

  // 表格切换事件处理函数
  const handleTableToggle = (event) => {
    console.log('🔄 表格切换事件触发', event.target);
    const button = event.target.closest('.table-toggle-btn');
    if (button) {
      const tableIndex = parseInt(button.getAttribute('data-table-index'));
      console.log('📊 切换表格索引:', tableIndex);
      if (!isNaN(tableIndex)) {
        toggleTableScrollMode(tableIndex);
        updateTableDisplay(tableIndex);
      }
    }
  };

  // 添加鼠标拖拽滚动支持
  const addDragScrollSupport = (container) => {
    let isDown = false;
    let startX;
    let scrollLeft;

    const handleMouseDown = (e) => {
      isDown = true;
      container.style.cursor = 'grabbing';
      startX = e.pageX - container.offsetLeft;
      scrollLeft = container.scrollLeft;
      e.preventDefault();
    };

    const handleMouseLeave = () => {
      isDown = false;
      container.style.cursor = 'grab';
    };

    const handleMouseUp = () => {
      isDown = false;
      container.style.cursor = 'grab';
    };

    const handleMouseMove = (e) => {
      if (!isDown) return;
      e.preventDefault();
      const x = e.pageX - container.offsetLeft;
      const walk = (x - startX) * 2; // 调整滚动速度
      container.scrollLeft = scrollLeft - walk;
    };

    // 存储事件处理器以便后续移除
    container._dragHandlers = {
      mousedown: handleMouseDown,
      mouseleave: handleMouseLeave,
      mouseup: handleMouseUp,
      mousemove: handleMouseMove,
    };

    container.addEventListener('mousedown', handleMouseDown);
    container.addEventListener('mouseleave', handleMouseLeave);
    container.addEventListener('mouseup', handleMouseUp);
    container.addEventListener('mousemove', handleMouseMove);
    container.style.cursor = 'grab';
  };

  // 移除鼠标拖拽滚动支持
  const removeDragScrollSupport = (container) => {
    if (container._dragHandlers) {
      container.removeEventListener('mousedown', container._dragHandlers.mousedown);
      container.removeEventListener('mouseleave', container._dragHandlers.mouseleave);
      container.removeEventListener('mouseup', container._dragHandlers.mouseup);
      container.removeEventListener('mousemove', container._dragHandlers.mousemove);
      delete container._dragHandlers;
      container.style.cursor = 'default';
    }
  };

  // 🔧 智能检测表格是否需要滚动
  const shouldTableScroll = (tableContainer) => {
    const table = tableContainer.querySelector('table');
    if (!table) return false;

    // 获取表格的实际宽度和容器宽度
    const tableWidth = table.scrollWidth;
    const containerWidth = tableContainer.clientWidth;

    console.log('📏 表格宽度检测:', {
      tableWidth,
      containerWidth,
      needsScroll: tableWidth > containerWidth,
    });

    // 如果表格宽度大于容器宽度，则需要滚动
    return tableWidth > containerWidth;
  };

  // 更新表格显示状态
  const updateTableDisplay = (tableIndex) => {
    console.log('🎨 更新表格显示状态，索引:', tableIndex);

    // 修正选择器：分别查找表格容器和按钮
    const tableContainer = document.querySelector(
      `.table-container[data-table-index="${tableIndex}"]`,
    );
    const toggleBtn = document.querySelector(
      `button.table-toggle-btn[data-table-index="${tableIndex}"]`,
    );
    const toggleText = toggleBtn?.querySelector('.toggle-text');

    console.log('🔍 找到的元素:', { tableContainer, toggleBtn, toggleText });

    if (tableContainer && toggleBtn) {
      const isScrollMode = getTableScrollMode(tableIndex);
      console.log('📋 当前模式:', isScrollMode ? '滚动模式' : '自适应模式');

      // 🔧 智能检测：如果表格不需要滚动，自动添加特殊样式
      const needsScroll = shouldTableScroll(tableContainer);

      if (isScrollMode) {
        // 滚动模式：表格可以左右滚动，文字不换行
        tableContainer.classList.add('scroll-mode');
        tableContainer.classList.remove('wrap-mode');

        // 🔧 新增：如果表格不需要滚动，添加特殊类名
        if (!needsScroll) {
          tableContainer.classList.add('no-scroll-needed');
          console.log('📝 表格不需要滚动，添加 no-scroll-needed 类');
        } else {
          tableContainer.classList.remove('no-scroll-needed');
        }

        if (toggleText) toggleText.textContent = '切换到自适应'; // 显示下一个模式

        // 添加鼠标拖拽支持
        addDragScrollSupport(tableContainer);
        console.log('✅ 已切换到滚动模式');
      } else {
        // 自适应模式：表格适应容器宽度，文字换行
        tableContainer.classList.add('wrap-mode');
        tableContainer.classList.remove('scroll-mode', 'no-scroll-needed');
        if (toggleText) toggleText.textContent = '切换到滚动'; // 显示下一个模式

        // 移除拖拽支持
        removeDragScrollSupport(tableContainer);
        console.log('✅ 已切换到自适应模式');
      }
    } else {
      console.log('❌ 未找到表格容器或按钮');
      console.log('🔍 调试信息 - 查找所有相关元素:');
      console.log('所有table-container:', document.querySelectorAll('.table-container'));
      console.log('所有toggle-btn:', document.querySelectorAll('.table-toggle-btn'));
    }
  };

  // 防抖渲染函数 - 优化MathJax渲染
  const debouncedRender = debounce(async (newContent) => {
    try {
      if (props.isPreformatted) {
        rawContent.value = newContent;
      } else {
        rawContent.value = renderMarkdown(newContent);
      }

      // 🔧 优化：只有当内容包含数学公式时才调用MathJax
      if (newContent.includes('$') || newContent.includes('\\[') || newContent.includes('\\(')) {
        await renderMath();
      }
    } catch (error) {
      console.error('内容更新失败:', error);
      emits('render-error', error);
    }
  }, props.debounceDelay);

  // 监听内容变化 - 优化MathJax渲染策略
  watch(
    () => props.content,
    async (newContent, oldContent) => {
      if (!newContent || !props.renderOnUpdate || newContent === oldContent) return;

      // 🔧 优化：检查是否包含数学公式
      const hasMath =
        newContent.includes('$') || newContent.includes('\\[') || newContent.includes('\\(');
      const contentDiff = Math.abs(newContent.length - (oldContent?.length || 0));

      if (contentDiff > 100) {
        // 大幅变化，立即渲染
        try {
          if (props.isPreformatted) {
            rawContent.value = newContent;
          } else {
            rawContent.value = renderMarkdown(newContent);
          }

          // 只有包含数学公式时才渲染MathJax
          if (hasMath) {
            await renderMath();
          }
        } catch (error) {
          console.error('内容更新失败:', error);
          emits('render-error', error);
        }
      } else {
        // 小幅变化，防抖渲染
        debouncedRender(newContent);
      }
    },
    { deep: true },
  );

  // 清理资源
  onUnmounted(() => {
    if (resizeObserver) {
      resizeObserver.disconnect();
    }
    // 清除 MathJax 渲染缓存
    if (window.MathJax && mathContainer.value) {
      window.MathJax.typesetClear([mathContainer.value]);
    }
    // 清理表格切换事件监听器
    if (mathContainer.value) {
      mathContainer.value.removeEventListener('click', handleTableToggle);
    }
  });

  // 错误捕获
  onErrorCaptured((err, _, info) => {
    console.error('组件错误:', err, info);
    emits('render-error', err);
    return false;
  });

  // 设置全局函数供HTML中的onclick使用
  onMounted(() => {
    // 添加事件监听器到容器
    if (mathContainer.value) {
      mathContainer.value.addEventListener('click', handleTableToggle);
    }
  });
</script>

<template>
  <div
    ref="mathContainer"
    class="markdown-content prose max-w-none tex2jax_process"
    v-html="rawContent"
  ></div>
</template>
<style>
  .markdown-content {
    font-family: 'LXGW WenKai', serif !important;
    overflow-wrap: break-word;
    color: var(--theme-color); /* 跟随主题 */
    background: var(--theme-bg);
    border-radius: 8px;
    padding: 0px;
    box-shadow: none;
    box-sizing: border-box;
    line-height: 1.6; /* 调整为更舒适的行高 */
    font-size: 20px !important; /* 增大基础字体大小 */
    max-width: 100%;
  }
  .markdown-content * {
    font-family: 'LXGW WenKai', serif !important;
    max-width: 100%;
    box-sizing: border-box;
  }
  .markdown-content .md-indent {
    display: inline-block;
    width: 2em;
  }

  /* 数学公式样式优化 */
  .markdown-content :deep(.mjx-container) {
    overflow-x: auto;
    overflow-y: hidden;
    padding: 0.25rem 0;
    margin: 0.5rem 0;
    transition: all 0.2s ease;
  }

  .markdown-content :deep(.mjx-container:hover) {
    background-color: rgba(0, 0, 0, 0.03);
  }

  /* 移动端优化 */
  @media (max-width: 640px) {
    .markdown-content :deep(.mjx-container) {
      min-width: auto !important;
      width: 100% !important;
    }
  }
  /* 表格容器 - 默认自适应模式 */
  .markdown-content .table-container {
    width: 100%;
    max-width: 100%; /* 确保容器不超出父元素宽度 */
    overflow: visible; /* 默认不滚动 */
    margin: 1rem 0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    box-sizing: border-box; /* 确保边框和内边距包含在宽度内 */
    /* 🔧 修复：确保内容不被隐藏 */
    padding: 0; /* 移除内边距 */
    position: relative; /* 确保定位正确 */
    left: 0; /* 确保不会向左偏移 */
    right: 0; /* 确保不会向右偏移 */
  }

  /* 滚动模式的表格容器 - 提高优先级 */
  .markdown-content .table-container.scroll-mode {
    overflow-x: auto !important;
    overflow-y: visible !important;
    /* 显示滚动条 */
    scrollbar-width: thin !important; /* Firefox */
    scrollbar-color: #cbd5e1 #f1f5f9 !important; /* Firefox */
    /* 🔧 修复：确保表格内容不够宽时不显示空白 */
    display: flex !important;
    flex-direction: column !important;
  }

  /* 滚动模式显示滚动条 - 提高优先级 */
  .markdown-content .table-container.scroll-mode::-webkit-scrollbar {
    display: block !important; /* 强制显示滚动条 */
    width: 8px !important;
    height: 8px !important;
  }

  /* 自适应模式的表格容器 */
  .markdown-content .table-container.wrap-mode {
    overflow: visible !important;
  }

  .markdown-content .table-container.wrap-mode table {
    table-layout: fixed !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  .markdown-content .table-container.wrap-mode th,
  .markdown-content .table-container.wrap-mode td {
    word-wrap: break-word !important;
    word-break: break-word !important;
    white-space: normal !important;
    overflow-wrap: break-word !important;
  }

  /* 通用表格样式 */
  .markdown-content table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    border-radius: 8px;
    background: var(--table-bg);
    color: var(--theme-color);
    border: 1px solid var(--table-border-color);
    table-layout: auto; /* 恢复：自动表格布局，让内容决定列宽 */
    box-sizing: border-box;
  }

  /* 🔧 滚动模式下的表格特殊处理 */
  .markdown-content .table-container.scroll-mode table {
    min-width: 100% !important; /* 确保表格至少占满容器宽度 */
    width: max-content !important; /* 根据内容自动调整宽度 */
  }

  /* 🔧 不需要滚动的表格特殊处理 - 消除右侧空白 */
  .markdown-content .table-container.scroll-mode.no-scroll-needed {
    overflow-x: visible !important; /* 不显示水平滚动条 */
    display: block !important; /* 恢复正常块级显示 */
  }

  .markdown-content .table-container.scroll-mode.no-scroll-needed table {
    width: 100% !important; /* 表格占满容器宽度 */
    min-width: auto !important; /* 移除最小宽度限制 */
  }

  .markdown-content th,
  .markdown-content td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid var(--table-border-color);
    border-right: 1px solid var(--table-border-color);
    vertical-align: top;
    word-wrap: break-word;
    overflow-wrap: break-word; /* 更好的换行支持 */
    hyphens: auto; /* 自动连字符 */
  }

  .markdown-content th:last-child,
  .markdown-content td:last-child {
    border-right: none; /* Remove border from the last column */
  }

  .markdown-content th {
    background: var(--table-header-bg);
    color: var(--theme-color);
    font-weight: 600;
    border-bottom-width: 2px;
  }

  .markdown-content tr:last-child td {
    border-bottom: none;
  }

  .markdown-content td {
    background: var(--table-bg);
    color: var(--theme-color);
  }

  .markdown-content tr:nth-child(even) td {
    background-color: var(--table-row-alt-bg);
  }

  .markdown-content tr:hover td {
    background-color: var(--table-header-bg);
    transition: background-color 0.2s ease;
  }

  /* 响应式表格 */
  @media (max-width: 768px) {
    .markdown-content {
      font-size: 18px !important; /* 移动端也增大字体 */
      line-height: 1.6; /* 保持舒适的行高 */
    }

    .markdown-content h1,
    .markdown-content h2,
    .markdown-content h3 {
      margin-top: 0.8em;
      margin-bottom: 0.4em;
      line-height: 1.3;
    }

    .markdown-content p,
    .markdown-content ul,
    .markdown-content ol {
      margin-top: 1em; /* 增加段落间距 */
      margin-bottom: 1em;
    }

    .markdown-content .table-container {
      margin: 0.5rem 0;
      border-radius: 6px;
      /* 🔧 修复：移动端也移除padding，避免内容偏移 */
      padding: 0;
    }

    .markdown-content table {
      font-size: 14px;
      width: 100%;
      /* 🔧 修复：移除display: block，保持正常表格布局 */
      display: table;
      /* 🔧 修复：移除overflow-x: auto，由容器控制滚动 */
      overflow: visible;
      white-space: normal; /* 允许文字换行 */
    }

    .markdown-content th,
    .markdown-content td {
      padding: 8px 10px;
      white-space: normal;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    .markdown-content th {
      font-size: 13px;
      font-weight: 600;
    }
  }

  /* 错误提示样式 */
  .mathjax-error {
    background-color: var(--theme-error-bg);
    border: 1px solid var(--theme-error-border);
    border-radius: 6px;
    padding: 12px;
    margin: 8px 0;
    color: var(--theme-error-text);
    font-size: 0.875rem;
  }

  /* 代码块样式优化 */
  .markdown-content pre {
    background: var(--table-row-alt-bg);
    border: 1px solid #e5e5d7;
    border-radius: 6px;
    color: var(--theme-color);
    font-size: 1em;
    box-shadow: none;
    padding: 10px 14px;
    margin: 1em 0;
  }

  .markdown-content code {
    background: var(--table-header-bg);
    color: var(--theme-color);
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 1em;
  }

  /* 链接样式优化 */
  .markdown-content a {
    color: var(--theme-link-color);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-bottom-color 0.2s ease;
  }

  .markdown-content a:hover {
    border-bottom-color: var(--theme-link-color);
  }

  /* 图片样式优化 */
  .markdown-content img {
    max-width: 100%;
    height: auto;
    border-radius: 6px;
    margin: 1rem 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background: var(--image-bg-color);
  }

  /* 引用块样式 */
  .markdown-content blockquote {
    margin: 1em 0;
    padding: 0.8em 1.2em;
    border-left: 5px solid var(--table-border-color);
    border-radius: 0 8px 8px 0;
    background: var(--blockquote-bg);
    color: var(--theme-color);
    font-style: italic;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  /* 桌面端优化：表格、代码块、引用等护眼简洁风格 */
  @media (min-width: 641px) {
    .markdown-content .table-container {
      background: var(--table-row-alt-bg);
      box-shadow: none;
      border-radius: 6px;
      margin: 1.2rem 0;
      /* 🔧 修复：完全移除左右padding，避免内容被"藏起来" */
      padding: 0.5rem 0;
    }
    .markdown-content table {
      background: var(--table-bg);
      border: 1px solid #e5e5d7;
      border-radius: 6px;
      box-shadow: none;
      min-width: auto; /* 修改：桌面端也允许表格自适应宽度 */
      width: 100%; /* 添加：确保表格占满容器宽度 */
    }
    .markdown-content th {
      background: var(--table-header-bg);
      color: var(--theme-color);
      font-weight: 600;
      border-bottom: 2px solid #e5e5d7;
    }
    .markdown-content td {
      background: var(--table-bg);
      color: var(--theme-color);
    }
    .markdown-content tr:nth-child(even) td {
      background-color: var(--table-row-alt-bg);
    }
    .markdown-content tr:hover td {
      background-color: var(--table-header-bg);
      transition: background-color 0.2s ease;
    }
    .markdown-content pre {
      background: var(--theme-bg);
      border: 1px solid #e5e5d7;
      border-radius: 6px;
      color: var(--theme-color);
      font-size: 1em;
      box-shadow: none;
      padding: 14px 18px;
      margin: 1.2em 0;
    }
    .markdown-content code {
      background: var(--theme-bg);
      color: var(--theme-color);
      border-radius: 4px;
      padding: 2px 6px;
      font-size: 1em;
    }
    .markdown-content blockquote {
      background: var(--blockquote-bg);
      border-left-color: var(--table-border-color);
      padding: 1.2em 1.8em;
    }
    .markdown-content a {
      color: var(--theme-link-color);
      border-bottom: 1px dashed var(--theme-link-color);
      text-decoration: none;
      transition: border-bottom-color 0.2s;
    }
    .markdown-content a:hover {
      border-bottom-color: var(--theme-link-color);
    }
    .markdown-content img {
      border-radius: 6px;
      box-shadow: none;
      margin: 1rem 0;
      max-width: 100%;
      background: var(--image-bg-color);
    }
  }

  /* 移动端样式保持原样 */

  /* 🚀 智能数学公式块样式 - 适配三种背景模式 */

  /* 纯白背景模式（默认） */
  .markdown-content .smart-math-block {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    color: #2c3e50;
    padding: 1.5rem;
    border-radius: 12px;
    margin: 1rem 0;
    border: 2px solid #ffcc02;
    border-left: 6px solid #ff9800;
    position: relative;
    overflow: hidden;
    font-family: 'LXGW WenKai', serif;
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.1);
    transition: all 0.3s ease;
  }

  /* 日间护眼模式 */
  .markdown-content.eye-protection .smart-math-block {
    background: linear-gradient(135deg, #f9f7f4 0%, #f1ede8 100%);
    color: #3e2723;
    border: 2px solid #e6dcc6;
    border-left: 6px solid #8bc34a;
    box-shadow: 0 4px 12px rgba(139, 195, 74, 0.15);
  }

  /* 夜间护眼模式 */
  .markdown-content.dark-mode .smart-math-block {
    background: linear-gradient(135deg, #2d2d2d 0%, #3a3a3a 100%);
    color: #e0e0e0;
    border: 2px solid #555;
    border-left: 6px solid #ff9800;
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.2);
  }

  /* 智能数学公式标识 - 三种模式 */
  .markdown-content .smart-math-block::before {
    content: '🧮 智能公式';
    position: absolute;
    top: 0.8rem;
    right: 0.8rem;
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7em;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
  }

  .markdown-content.eye-protection .smart-math-block::before {
    background: linear-gradient(135deg, #8bc34a, #689f38);
    box-shadow: 0 2px 8px rgba(139, 195, 74, 0.4);
  }

  .markdown-content.dark-mode .smart-math-block::before {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.4);
  }

  /* 🚀 智能数学公式块（无标识版本） - 适配三种背景模式 */

  /* 纯白背景模式（默认） */
  .markdown-content .smart-math-block-silent {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    color: #2c3e50;
    padding: 1.5rem;
    border-radius: 12px;
    margin: 1rem 0;
    border: 2px solid #ffcc02;
    border-left: 6px solid #ff9800;
    position: relative;
    overflow: hidden;
    font-family: 'LXGW WenKai', serif;
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.1);
    transition: all 0.3s ease;
  }

  /* 日间护眼模式 */
  .markdown-content.eye-protection .smart-math-block-silent {
    background: linear-gradient(135deg, #f9f7f4 0%, #f1ede8 100%);
    color: #3e2723;
    border: 2px solid #e6dcc6;
    border-left: 6px solid #8bc34a;
    box-shadow: 0 4px 12px rgba(139, 195, 74, 0.15);
  }

  /* 夜间护眼模式 */
  .markdown-content.dark-mode .smart-math-block-silent {
    background: linear-gradient(135deg, #2d2d2d 0%, #3a3a3a 100%);
    color: #e0e0e0;
    border: 2px solid #555;
    border-left: 6px solid #ff9800;
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.2);
  }

  /* 🚀 智能代码块样式 - 适配三种背景模式 */

  /* 纯白背景模式（默认） */
  .markdown-content .smart-code-block {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #2c3e50;
    padding: 1.5rem;
    border-radius: 12px;
    margin: 1rem 0;
    border: 2px solid #e3f2fd;
    border-left: 6px solid #2196f3;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.6;
    overflow-x: auto;
    position: relative;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.1);
    transition: all 0.3s ease;
  }

  /* 日间护眼模式 */
  .markdown-content.eye-protection .smart-code-block {
    background: linear-gradient(135deg, #f9f7f4 0%, #f1ede8 100%);
    color: #3e2723;
    border: 2px solid #e6dcc6;
    border-left: 6px solid #8bc34a;
    box-shadow: 0 4px 12px rgba(139, 195, 74, 0.15);
  }

  /* 夜间护眼模式 */
  .markdown-content.dark-mode .smart-code-block {
    background: linear-gradient(135deg, #2d2d2d 0%, #3a3a3a 100%);
    color: #e0e0e0;
    border: 2px solid #555;
    border-left: 6px solid #ff9800;
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.2);
  }

  /* 智能解析标识 - 三种模式 */
  .markdown-content .smart-code-block::before {
    content: '🧠 智能解析';
    position: absolute;
    top: 0.8rem;
    right: 0.8rem;
    background: linear-gradient(135deg, #2196f3, #1976d2);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.75em;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
  }

  .markdown-content.eye-protection .smart-code-block::before {
    background: linear-gradient(135deg, #8bc34a, #689f38);
    box-shadow: 0 2px 8px rgba(139, 195, 74, 0.4);
  }

  .markdown-content.dark-mode .smart-code-block::before {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.4);
  }

  /* 🌟 统一的加粗样式 - 适配三种背景模式 */

  /* 纯白背景模式（默认） */
  .markdown-content strong {
    color: #1976d2 !important;
    font-weight: 700;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  /* 日间护眼模式 */
  body.eye-protection .markdown-content strong {
    color: #8bc34a !important;
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9) !important;
    box-shadow: 0 1px 3px rgba(139, 195, 74, 0.1);
  }

  /* 夜间护眼模式 */
  body.dark-mode .markdown-content strong {
    color: #ff9800 !important;
    background: linear-gradient(135deg, #37474f, #546e7a) !important;
    box-shadow: 0 1px 3px rgba(255, 152, 0, 0.2);
  }

  /* 智能代码块中的加粗样式 - 适配三种背景模式 */
  .markdown-content .smart-code-block strong {
    color: #1976d2 !important;
    font-weight: 700;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  body.eye-protection .markdown-content .smart-code-block strong {
    color: #8bc34a !important;
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9) !important;
    box-shadow: 0 1px 3px rgba(139, 195, 74, 0.1);
  }

  body.dark-mode .markdown-content .smart-code-block strong {
    color: #ff9800 !important;
    background: linear-gradient(135deg, #37474f, #546e7a) !important;
    box-shadow: 0 1px 3px rgba(255, 152, 0, 0.2);
  }

  /* 🌟 统一的斜体样式 - 适配三种背景模式 */

  /* 纯白背景模式（默认） */
  .markdown-content em {
    color: #1976d2 !important;
    font-style: italic;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  /* 日间护眼模式 */
  body.eye-protection .markdown-content em {
    color: #8bc34a !important;
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9) !important;
    box-shadow: 0 1px 3px rgba(139, 195, 74, 0.1);
  }

  /* 夜间护眼模式 */
  body.dark-mode .markdown-content em {
    color: #ff9800 !important;
    background: linear-gradient(135deg, #37474f, #546e7a) !important;
    box-shadow: 0 1px 3px rgba(255, 152, 0, 0.2);
  }

  /* 智能代码块中的斜体样式 - 适配三种背景模式 */
  .markdown-content .smart-code-block em {
    color: #1976d2 !important;
    font-style: italic;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  body.eye-protection .markdown-content .smart-code-block em {
    color: #8bc34a !important;
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9) !important;
    box-shadow: 0 1px 3px rgba(139, 195, 74, 0.1);
  }

  body.dark-mode .markdown-content .smart-code-block em {
    color: #ff9800 !important;
    background: linear-gradient(135deg, #37474f, #546e7a) !important;
    box-shadow: 0 1px 3px rgba(255, 152, 0, 0.2);
  }

  /* 🌟 统一的行内代码样式 - 适配三种背景模式 */

  /* 纯白背景模式（默认） */
  .markdown-content code {
    color: #e91e63 !important;
    background: linear-gradient(135deg, #fce4ec, #f8bbd9);
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
    transition: all 0.2s ease;
  }

  /* 日间护眼模式 */
  body.eye-protection .markdown-content code {
    color: #8bc34a !important;
    background: linear-gradient(135deg, #f1f8e9, #dcedc8) !important;
    box-shadow: 0 1px 3px rgba(139, 195, 74, 0.1);
  }

  /* 夜间护眼模式 */
  body.dark-mode .markdown-content code {
    color: #ff9800 !important;
    background: linear-gradient(135deg, #424242, #616161) !important;
    box-shadow: 0 1px 3px rgba(255, 152, 0, 0.2);
  }

  /* 智能代码块中的数学公式样式 */
  .markdown-content .smart-code-block :deep(.mjx-container) {
    background: linear-gradient(135deg, #fff3e0, #ffe0b2) !important;
    border: 1px solid #ffcc02;
    padding: 0.4rem 0.6rem;
    border-radius: 6px;
    margin: 0.3rem 0;
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.2);
  }

  /* 🌟 统一的加粗样式 - 适配三种背景模式 */

  /* 纯白背景模式（默认） */
  .markdown-content strong {
    color: #1976d2 !important;
    font-weight: 700;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  /* 日间护眼模式 */
  body.eye-protection .markdown-content strong {
    color: #8bc34a !important;
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9) !important;
    box-shadow: 0 1px 3px rgba(139, 195, 74, 0.1);
  }

  /* 夜间护眼模式 */
  body.dark-mode .markdown-content strong {
    color: #ff9800 !important;
    background: linear-gradient(135deg, #37474f, #546e7a) !important;
    box-shadow: 0 1px 3px rgba(255, 152, 0, 0.2);
  }

  /* 🌟 统一的斜体样式 - 适配三种背景模式 */

  /* 纯白背景模式（默认） */
  .markdown-content em {
    color: #1976d2 !important;
    font-style: italic;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  /* 日间护眼模式 */
  body.eye-protection .markdown-content em {
    color: #8bc34a !important;
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9) !important;
    box-shadow: 0 1px 3px rgba(139, 195, 74, 0.1);
  }

  /* 夜间护眼模式 */
  body.dark-mode .markdown-content em {
    color: #ff9800 !important;
    background: linear-gradient(135deg, #37474f, #546e7a) !important;
    box-shadow: 0 1px 3px rgba(255, 152, 0, 0.2);
  }

  /* 🌟 统一的行内代码样式 - 适配三种背景模式 */

  /* 纯白背景模式（默认） */
  .markdown-content code {
    color: #e91e63 !important;
    background: linear-gradient(135deg, #fce4ec, #f8bbd9);
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
    transition: all 0.2s ease;
  }

  /* 日间护眼模式 */
  body.eye-protection .markdown-content code {
    color: #8bc34a !important;
    background: linear-gradient(135deg, #f1f8e9, #dcedc8) !important;
    box-shadow: 0 1px 3px rgba(139, 195, 74, 0.1);
  }

  /* 夜间护眼模式 */
  body.dark-mode .markdown-content code {
    color: #ff9800 !important;
    background: linear-gradient(135deg, #424242, #616161) !important;
    box-shadow: 0 1px 3px rgba(255, 152, 0, 0.2);
  }

  /* 移动端智能代码块优化 */
  @media (max-width: 640px) {
    .markdown-content .smart-code-block {
      padding: 1rem;
      font-size: 0.85em;
      margin: 0.8rem 0;
    }

    .markdown-content .smart-code-block::before {
      font-size: 0.6em;
      padding: 0.1rem 0.3rem;
    }
  }

  /* 表格切换功能样式 */
  .table-wrapper {
    margin: 1rem 0;
  }

  .table-controls {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  /* 滚动提示已移除 */

  @keyframes pulse {
    0%,
    100% {
      opacity: 0.7;
    }
    50% {
      opacity: 1;
    }
  }

  .table-toggle-btn {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    background-color: #dbeafe;
    color: #1d4ed8;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-family: 'LXGW WenKai', serif;
  }

  .table-toggle-btn:hover {
    background-color: #bfdbfe;
  }

  .table-toggle-btn svg {
    width: 0.75rem;
    height: 0.75rem;
  }

  /* 默认模式：自适应宽度，文字换行 */
  .table-container.wrap-mode {
    width: 100%;
    overflow: visible;
  }

  .table-container.wrap-mode table {
    width: 100%;
    table-layout: auto; /* 自动布局，让列宽根据内容调整 */
  }

  .table-container.wrap-mode td,
  .table-container.wrap-mode th {
    word-wrap: break-word;
    word-break: break-word;
    white-space: normal;
    padding: 8px 12px; /* 增加内边距 */
    vertical-align: top; /* 顶部对齐 */
  }

  /* 智能列宽分配 - 让列宽根据内容自适应 */
  .table-container.wrap-mode th:nth-child(1),
  .table-container.wrap-mode td:nth-child(1) {
    width: auto; /* 第一列：关系描述，根据内容调整 */
    min-width: 80px;
    max-width: 25%;
  }

  .table-container.wrap-mode th:nth-child(2),
  .table-container.wrap-mode td:nth-child(2) {
    width: auto; /* 第二列：比例数字，紧凑显示 */
    min-width: 60px;
    max-width: 15%;
    white-space: nowrap; /* 数字不换行 */
  }

  .table-container.wrap-mode th:nth-child(3),
  .table-container.wrap-mode td:nth-child(3) {
    width: auto; /* 第三列：详细说明，占用剩余空间 */
    min-width: 200px;
  }

  .table-container.wrap-mode th:nth-child(n + 4),
  .table-container.wrap-mode td:nth-child(n + 4) {
    width: auto; /* 其他列自动分配 */
  }

  /* 滚动模式：自适应宽度，文字不换行，可左右滚动 */
  .table-container.scroll-mode {
    width: auto; /* 改为自动宽度，避免右侧空白 */
    max-width: 100%; /* 最大不超过父容器宽度 */
    overflow-x: auto;
    overflow-y: visible;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    /* 确保滚动容器有足够的空间 */
    min-width: 0;
    position: relative;
    /* 显示滚动条，方便用户操作 */
    scrollbar-width: thin; /* Firefox - 显示细滚动条 */
    scrollbar-color: #cbd5e1 #f1f5f9; /* Firefox - 滚动条颜色 */
  }

  /* 滚动模式滚动条样式 - 提高优先级 */
  .markdown-content .table-container.scroll-mode::-webkit-scrollbar-track {
    background: #f1f5f9 !important; /* 滚动条轨道颜色 */
    border-radius: 4px !important;
  }

  .markdown-content .table-container.scroll-mode::-webkit-scrollbar-thumb {
    background: #cbd5e1 !important; /* 滚动条滑块颜色 */
    border-radius: 4px !important;
  }

  .markdown-content .table-container.scroll-mode::-webkit-scrollbar-thumb:hover {
    background: #94a3b8 !important; /* 滚动条滑块悬停颜色 */
  }

  .table-container.scroll-mode table {
    width: auto; /* 改为自动宽度，根据内容调整 */
    white-space: nowrap;
    /* 确保表格不被压缩，但允许根据内容调整宽度 */
    min-width: max-content; /* 最小宽度为内容所需宽度 */
    table-layout: auto; /* 改为自动布局，让列宽根据内容调整 */
  }

  .table-container.scroll-mode table td,
  .table-container.scroll-mode table th {
    white-space: nowrap;
  }

  .table-container.scroll-mode td,
  .table-container.scroll-mode th {
    white-space: nowrap;
    padding: 12px 16px; /* 增加内边距 */
    vertical-align: top;
  }

  /* 滚动模式下为不同列设置合适的最小宽度 - 改为更灵活的设置 */
  .table-container.scroll-mode th:nth-child(1),
  .table-container.scroll-mode td:nth-child(1) {
    min-width: 80px; /* 选项列 - 减少最小宽度 */
    width: auto; /* 改为自动宽度 */
  }

  .table-container.scroll-mode th:nth-child(2),
  .table-container.scroll-mode td:nth-child(2) {
    min-width: 150px; /* 减少最小宽度，让内容决定实际宽度 */
    width: auto; /* 改为自动宽度 */
  }

  .table-container.scroll-mode th:nth-child(3),
  .table-container.scroll-mode td:nth-child(3) {
    min-width: 150px; /* 减少最小宽度 */
    width: auto; /* 改为自动宽度，确保数学公式有足够空间 */
  }

  .table-container.scroll-mode th:nth-child(4),
  .table-container.scroll-mode td:nth-child(4) {
    min-width: 120px; /* 减少最小宽度 */
    width: auto; /* 改为自动宽度 */
  }

  /* 其他列使用默认最小宽度 */
  .table-container.scroll-mode th:nth-child(n + 5),
  .table-container.scroll-mode td:nth-child(n + 5) {
    min-width: 100px; /* 减少其他列的最小宽度 */
    width: auto; /* 改为自动宽度 */
  }

  /* 滚动条已隐藏 - 使用拖拽滚动代替 */

  /* 保持触摸滚动支持 */
  .table-container.scroll-mode {
    -webkit-overflow-scrolling: touch; /* iOS 触摸滚动优化 */
  }

  /* 滚动模式下数学公式的特殊处理 */
  .table-container.scroll-mode .mjx-container {
    display: inline-block;
    margin: 0 4px;
    min-width: auto;
    max-width: none; /* 确保数学公式不被限制宽度 */
  }

  /* 滚动模式下确保数学公式不被压缩 */
  .table-container.scroll-mode td .mjx-container,
  .table-container.scroll-mode th .mjx-container {
    white-space: nowrap;
    overflow: visible;
    flex-shrink: 0; /* 防止被flex压缩 */
  }

  /* 滚动模式下的表格单元格内容布局优化 */
  .table-container.scroll-mode td,
  .table-container.scroll-mode th {
    display: table-cell; /* 确保使用表格布局 */
    box-sizing: border-box;
  }

  /* 滚动模式下包含数学公式的单元格特殊处理 */
  .table-container.scroll-mode td:has(.mjx-container),
  .table-container.scroll-mode th:has(.mjx-container) {
    min-width: 200px; /* 为包含数学公式的单元格提供更多空间 */
  }

  /* 修复在sy.vue中的显示问题 */
  .scroll-container .table-container.scroll-mode {
    /* 确保在外层滚动容器中正确显示 */
    overflow-x: auto !important;
    overflow-y: visible !important;
    width: 100% !important;
    max-width: none !important;
  }

  /* 确保表格内容不被截断，同时避免不必要的空白 */
  .scroll-container .table-container.scroll-mode table {
    width: auto !important;
    min-width: max-content !important;
    max-width: 100% !important; /* 添加最大宽度限制 */
    table-layout: auto !important;
  }

  /* 默认状态：自适应模式 */
  .table-container {
    width: 100%;
    overflow: visible;
  }

  .table-container table {
    width: 100%;
    table-layout: auto;
  }

  .table-container td,
  .table-container th {
    word-wrap: break-word;
    word-break: break-word;
    white-space: normal;
    max-width: 200px;
  }
</style>
