// 调试shuxue.vue组件渲染问题的测试脚本
const puppeteer = require('puppeteer');

async function testShuxueRendering() {
  console.log('🔍 开始测试shuxue.vue组件渲染...');
  
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: { width: 1200, height: 800 }
  });
  
  try {
    const page = await browser.newPage();
    
    // 监听控制台消息
    page.on('console', msg => {
      console.log('浏览器控制台:', msg.text());
    });
    
    // 监听页面错误
    page.on('pageerror', error => {
      console.error('页面错误:', error.message);
    });
    
    console.log('🌐 访问测试页面...');
    await page.goto('http://localhost:6001/fb/sy?kjid=1_3e_26bha6h&type=syzc&id=123', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });
    
    // 等待页面加载完成
    await page.waitForTimeout(3000);
    
    // 检查MathJax是否正确加载
    const mathJaxStatus = await page.evaluate(() => {
      return {
        mathJaxLoaded: typeof window.MathJax !== 'undefined',
        mathJaxVersion: window.MathJax?.version || '未加载',
        mjxElements: document.querySelectorAll('mjx-container').length,
        mathElements: document.querySelectorAll('.mjx-container').length,
        texElements: document.querySelectorAll('.tex2jax_process').length
      };
    });
    
    console.log('🧮 MathJax状态:', mathJaxStatus);
    
    // 检查表格渲染
    const tableStatus = await page.evaluate(() => {
      const tables = document.querySelectorAll('table');
      const tableContainers = document.querySelectorAll('.table-container');
      const tableWrappers = document.querySelectorAll('.table-wrapper');
      
      return {
        tableCount: tables.length,
        tableContainerCount: tableContainers.length,
        tableWrapperCount: tableWrappers.length,
        scrollModeTables: document.querySelectorAll('.table-container.scroll-mode').length,
        wrapModeTables: document.querySelectorAll('.table-container.wrap-mode').length
      };
    });
    
    console.log('📊 表格状态:', tableStatus);
    
    // 检查CSS样式问题
    const styleStatus = await page.evaluate(() => {
      const shuxueComponent = document.querySelector('.markdown-content');
      if (!shuxueComponent) return { found: false };
      
      const computedStyle = window.getComputedStyle(shuxueComponent);
      
      return {
        found: true,
        fontFamily: computedStyle.fontFamily,
        fontSize: computedStyle.fontSize,
        color: computedStyle.color,
        backgroundColor: computedStyle.backgroundColor,
        width: computedStyle.width,
        height: computedStyle.height,
        overflow: computedStyle.overflow
      };
    });
    
    console.log('🎨 样式状态:', styleStatus);
    
    // 截图保存
    await page.screenshot({ 
      path: 'shuxue-debug-screenshot.png',
      fullPage: true 
    });
    
    console.log('📸 截图已保存为 shuxue-debug-screenshot.png');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await browser.close();
  }
}

testShuxueRendering().catch(console.error); 